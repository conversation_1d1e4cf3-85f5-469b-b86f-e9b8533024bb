import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { User } from '../entities/user.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { UserStatus } from '../enum/user-status.enum';

/**
 * Repository cho User
 */
@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {}

  /**
   * Tìm người dùng theo email
   * @param email Email cần tìm
   * @returns Thông tin người dùng hoặc null nếu không tìm thấy
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.repository.findOne({ where: { email } });
  }

  /**
   * Tìm người dùng theo ID
   * @param id ID cần tìm
   * @returns Thông tin người dùng hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<User | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm nhiều người dùng theo danh sách ID
   * @param ids Danh sách ID cần tìm
   * @returns Danh sách người dùng
   */
  async findByIds(ids: number[]): Promise<User[]> {
    return this.repository.find({
      where: { id: In(ids) },
    });
  }

  /**
   * Tìm tất cả người dùng thuộc một phòng ban
   * @param departmentId ID phòng ban
   * @returns Danh sách người dùng thuộc phòng ban
   */
  async findByDepartmentId(
    tenantId: number,
    departmentId: number,
  ): Promise<User[]> {
    return this.repository.find({
      where: { departmentId, tenantId },
      order: { fullName: 'ASC' },
    });
  }

  /**
   * Tìm người dùng theo employee ID
   * @param employeeId ID nhân viên
   * @returns Thông tin người dùng hoặc null nếu không tìm thấy
   */
  async findByEmployeeId(employeeId: number): Promise<User | null> {
    return this.repository.findOne({ where: { employeeId } });
  }

  /**
   * Đếm số lượng người dùng theo phòng ban
   * @returns Danh sách kết quả đếm theo phòng ban
   */
  async countByDepartments(
    tenantId: number,
  ): Promise<{ departmentId: number; count: number }[]> {
    const result = await this.repository
      .createQueryBuilder('user')
      .select('user.departmentId', 'departmentId')
      .addSelect('COUNT(user.id)', 'count')
      .where('user.departmentId IS NOT NULL AND user.tenantId = :tenantId', {
        tenantId,
      })
      .groupBy('user.departmentId')
      .getRawMany();

    return result.map((item) => ({
      departmentId: parseInt(item.departmentId),
      count: parseInt(item.count),
    }));
  }

  /**
   * Đếm số lượng tài khoản người dùng có liên kết với nhân viên
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Số lượng tài khoản có employeeId
   */
  async countUsersWithEmployee(tenantId: number): Promise<number> {
    const result = await this.repository
      .createQueryBuilder('user')
      .select('COUNT(user.id)', 'count')
      .where('user.employeeId IS NOT NULL AND user.tenantId = :tenantId', {
        tenantId,
      })
      .getRawOne();

    return parseInt(result.count) || 0;
  }

  /**
   * Tạo mới người dùng
   * @param data Dữ liệu người dùng
   * @returns Thông tin người dùng đã tạo
   */
  async create(data: Partial<User>): Promise<User> {
    const user = this.repository.create(data);
    return this.repository.save(user);
  }

  /**
   * Cập nhật thông tin người dùng
   * @param id ID người dùng cần cập nhật
   * @param data Dữ liệu cần cập nhật
   * @returns Thông tin người dùng đã cập nhật
   * @throws Error nếu không tìm thấy người dùng sau khi cập nhật
   */
  async update(id: number, data: Partial<User>): Promise<User> {
    await this.repository.update(id, data);
    const updatedUser = await this.findById(id);

    if (!updatedUser) {
      throw new Error(`User with ID ${id} not found after update`);
    }

    return updatedUser;
  }

  /**
   * Cập nhật trạng thái người dùng
   * @param id ID người dùng cần cập nhật
   * @param status Trạng thái mới
   * @returns Thông tin người dùng đã cập nhật
   */
  async updateStatus(id: number, status: UserStatus): Promise<User> {
    return this.update(id, { status });
  }

  /**
   * Tìm tất cả người dùng với phân trang và lọc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách người dùng đã phân trang
   */
  async findAll(
    tenantId: number,
    query: {
      page?: number;
      limit?: number;
      search?: string;
      sortBy?: string;
      sortDirection?: 'ASC' | 'DESC';
      status?: UserStatus;
      departmentId?: number;
      employeeId?: number;
      hasEmployee?: boolean;
      userType?: string;
    } = {},
  ): Promise<PaginatedResult<User>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      status,
      departmentId,
      employeeId,
      hasEmployee,
      userType,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('user');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('user.tenantId = :tenantId', { tenantId });

    // Apply filters if provided
    if (status) {
      queryBuilder.andWhere('user.status = :status', { status });
    }

    if (departmentId) {
      queryBuilder.andWhere('user.departmentId = :departmentId', { departmentId });
    }

    if (employeeId) {
      queryBuilder.andWhere('user.employeeId = :employeeId', { employeeId });
    }

    if (hasEmployee !== undefined) {
      if (hasEmployee) {
        queryBuilder.andWhere('user.employeeId IS NOT NULL');
      } else {
        queryBuilder.andWhere('user.employeeId IS NULL');
      }
    }

    if (userType) {
      queryBuilder.andWhere('user.userType = :userType', { userType });
    }

    // Apply search if provided
    if (search) {
      queryBuilder.andWhere(
        '(user.fullName ILIKE :search OR user.email ILIKE :search OR user.position ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`user.${sortBy}`, sortDirection);

    // Apply pagination
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }
}
