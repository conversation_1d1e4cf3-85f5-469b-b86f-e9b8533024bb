import { Injectable, Logger } from '@nestjs/common';
import { TodoRepository } from '../repositories/todo.repository';
import { ProjectMemberRepository } from '../repositories/project-member.repository';
import { TaskKrRepository } from '../repositories/task-kr.repository';
import { TodoScoreRepository } from '../repositories/todo-score.repository';
import { Todo } from '../entities/todo.entity';
import { TodoScore } from '../entities/todo-score.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { TODOLISTS_ERROR_CODES } from '../errors/todolists-error.code';
import { CreateTodoDto } from '../dto/todo/create-todo.dto';
import { UpdateTodoDto } from '../dto/todo/update-todo.dto';
import { TodoQueryDto } from '../dto/todo/todo-query.dto';
import { UpdateTodoStatusDto } from '../dto/todo/update-todo-status.dto';
import { BulkDeleteTodoDto } from '../dto/todo/bulk-delete-todo.dto';
import { BulkDeleteTodoResponseDto } from '../dto/todo/bulk-delete-todo-response.dto';
import { ScoreTodoDto } from '../dto/todo-score/score-todo.dto';
import { TodoStatus } from '../enum/todo-status.enum';
import { TodoPriority } from '../enum/todo-priority.enum';
import { ProjectMemberRole } from '../enum/project-member-role.enum';

/**
 * Service xử lý logic nghiệp vụ cho công việc
 */
@Injectable()
export class TodoService {
  private readonly logger = new Logger(TodoService.name);

  constructor(
    private readonly todoRepository: TodoRepository,
    private readonly projectMemberRepository: ProjectMemberRepository,
    private readonly taskKrRepository: TaskKrRepository,
    private readonly todoScoreRepository: TodoScoreRepository,
  ) {}

  /**
   * Tạo công việc mới
   * @param tenantId ID tenant
   * @param userId ID người dùng tạo công việc
   * @param createTodoDto Thông tin công việc
   * @returns Công việc đã tạo
   */
  async createTodo(
    tenantId: number,
    userId: number,
    createTodoDto: CreateTodoDto,
  ): Promise<Todo> {
    try {
      // Kiểm tra công việc cha nếu có
      if (createTodoDto.parentId) {
        const parentTodo = await this.todoRepository.findById(
          tenantId,
          createTodoDto.parentId,
        );
        if (!parentTodo) {
          throw new AppException(
            TODOLISTS_ERROR_CODES.PARENT_TODO_NOT_FOUND,
            `Không tìm thấy công việc cha với ID ${createTodoDto.parentId}`,
          );
        }
      }

      // Kiểm tra quyền tạo công việc trong dự án nếu có
      if (createTodoDto.categoryId) {
        const isMember = await this.projectMemberRepository.isProjectMember(
          tenantId,
          createTodoDto.categoryId,
          userId,
        );
        if (!isMember) {
          throw new AppException(
            TODOLISTS_ERROR_CODES.NOT_PROJECT_MEMBER,
            'Bạn không phải là thành viên của dự án này',
          );
        }
      }

      // Tạo công việc mới
      const now = Date.now();
      const todo = await this.todoRepository.create(tenantId, {
        title: createTodoDto.title,
        description: createTodoDto.description,
        assigneeId: createTodoDto.assigneeId,
        status: TodoStatus.PENDING,
        priority: createTodoDto.priority,
        expectedStars: createTodoDto.expectedStars || 3,
        createdBy: userId,
        createdAt: now,
        updatedAt: now,
        categoryId: createTodoDto.categoryId,
        parentId: createTodoDto.parentId,
      });

      // Tạo liên kết với key result nếu có
      if (createTodoDto.keyResultIds && createTodoDto.keyResultIds.length > 0) {
        const taskKrs = createTodoDto.keyResultIds.map((krId) => ({
          taskId: todo.id,
          krId,
        }));
        await this.taskKrRepository.createMany(tenantId, taskKrs);
      }

      return todo;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo công việc: ${error.message}`, error.stack);
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_CREATION_FAILED,
        `Tạo công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách công việc
   * @param tenantId ID tenant
   * @param query Tham số truy vấn
   * @returns Danh sách công việc đã phân trang
   */
  async findAllTodos(
    tenantId: number,
    query: TodoQueryDto,
  ): Promise<PaginatedResult<Todo>> {
    try {
      return await this.todoRepository.findAll(tenantId, query);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
        `Lấy danh sách công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết công việc
   * @param tenantId ID tenant
   * @param id ID công việc
   * @returns Thông tin chi tiết công việc
   */
  async findTodoById(tenantId: number, id: number): Promise<Todo> {
    const todo = await this.todoRepository.findById(tenantId, id);
    if (!todo) {
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
        `Không tìm thấy công việc với ID ${id}`,
      );
    }
    return todo;
  }

  /**
   * Lấy danh sách công việc con
   * @param tenantId ID tenant
   * @param parentId ID công việc cha
   * @param query Tham số truy vấn
   * @returns Danh sách công việc con đã phân trang
   */
  async findSubtasks(
    tenantId: number,
    parentId: number,
    query: TodoQueryDto,
  ): Promise<PaginatedResult<Todo>> {
    try {
      // Kiểm tra công việc cha tồn tại
      await this.findTodoById(tenantId, parentId);

      // Lấy danh sách công việc con
      return await this.todoRepository.findSubtasks(tenantId, parentId, query);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy danh sách công việc con: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
        `Lấy danh sách công việc con thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param userId ID người dùng cập nhật
   * @param updateTodoDto Thông tin cập nhật
   * @returns Công việc đã cập nhật
   */
  async updateTodo(
    tenantId: number,
    id: number,
    userId: number,
    updateTodoDto: UpdateTodoDto,
  ): Promise<Todo | null> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.findTodoById(tenantId, id);

      // Kiểm tra quyền cập nhật (chỉ người được giao hoặc người tạo mới có quyền cập nhật)
      if (todo.assigneeId !== userId && todo.createdBy !== userId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_TODO_ASSIGNEE,
          'Bạn không có quyền cập nhật công việc này',
        );
      }

      // Kiểm tra dự án nếu có thay đổi
      if (
        updateTodoDto.categoryId &&
        updateTodoDto.categoryId !== todo.categoryId
      ) {
        const isMember = await this.projectMemberRepository.isProjectMember(
          tenantId,
          updateTodoDto.categoryId,
          userId,
        );
        if (!isMember) {
          throw new AppException(
            TODOLISTS_ERROR_CODES.NOT_PROJECT_MEMBER,
            'Bạn không phải là thành viên của dự án này',
          );
        }
      }

      // Cập nhật công việc
      return await this.todoRepository.update(tenantId, id, {
        ...updateTodoDto,
        updatedAt: Date.now(),
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_UPDATE_FAILED,
        `Cập nhật công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật trạng thái công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param userId ID người dùng cập nhật
   * @param updateTodoStatusDto Thông tin trạng thái mới
   * @returns Công việc đã cập nhật
   */
  async updateTodoStatus(
    tenantId: number,
    id: number,
    userId: number,
    updateTodoStatusDto: UpdateTodoStatusDto,
  ): Promise<Todo | null> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.findTodoById(tenantId, id);

      // Kiểm tra quyền cập nhật trạng thái (chỉ người được giao hoặc người tạo mới có quyền cập nhật)
      if (todo.assigneeId !== userId && todo.createdBy !== userId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_TODO_ASSIGNEE,
          'Bạn không có quyền cập nhật trạng thái công việc này',
        );
      }

      // Cập nhật trạng thái công việc
      return await this.todoRepository.updateStatus(
        tenantId,
        id,
        updateTodoStatusDto.status,
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật trạng thái công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_STATUS_UPDATE_FAILED,
        `Cập nhật trạng thái công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param userId ID người dùng xóa
   * @returns true nếu xóa thành công
   */
  async deleteTodo(
    tenantId: number,
    id: number,
    userId: number,
  ): Promise<boolean> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.findTodoById(tenantId, id);

      // Kiểm tra quyền xóa (chỉ người tạo mới có quyền xóa)
      if (todo.createdBy !== userId) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_TODO_ASSIGNEE,
          'Bạn không có quyền xóa công việc này',
        );
      }

      // Xóa công việc
      const result = await this.todoRepository.delete(tenantId, id);
      if (!result) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_DELETE_FAILED,
          `Xóa công việc thất bại`,
        );
      }
      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa công việc: ${error.message}`, error.stack);
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_DELETE_FAILED,
        `Xóa công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Chấm điểm cho công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID công việc
   * @param userId ID người dùng chấm điểm
   * @param scoreTodoDto Thông tin chấm điểm
   * @returns Công việc đã được chấm điểm
   */
  async scoreTodo(
    tenantId: number,
    id: number,
    userId: number,
    scoreTodoDto: ScoreTodoDto,
  ): Promise<Todo | null> {
    try {
      // Kiểm tra công việc tồn tại
      const todo = await this.findTodoById(tenantId, id);

      // Kiểm tra trạng thái công việc (chỉ chấm điểm khi công việc đã hoàn thành)
      if (todo.status !== TodoStatus.COMPLETED) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TODO_NOT_COMPLETED,
          'Chỉ có thể chấm điểm cho công việc đã hoàn thành',
        );
      }

      // Kiểm tra quyền chấm điểm
      const canScore = await this.checkScoringPermission(
        tenantId,
        userId,
        todo,
      );
      if (!canScore) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.NOT_AUTHORIZED_TO_SCORE,
          'Bạn không có quyền chấm điểm cho công việc này',
        );
      }

      // Lưu thông tin chấm điểm
      const now = Date.now();
      await this.todoScoreRepository.create(tenantId, {
        todoId: id,
        scorerId: userId,
        awardedStars: scoreTodoDto.awardedStars,
        feedback: scoreTodoDto.feedback || null,
        createdAt: now,
      });

      // Cập nhật điểm cho công việc
      return await this.todoRepository.updateScore(
        tenantId,
        id,
        scoreTodoDto.awardedStars,
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi chấm điểm công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_SCORING_FAILED,
        `Chấm điểm công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra quyền chấm điểm
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId ID người dùng
   * @param todo Công việc cần chấm điểm
   * @returns true nếu có quyền chấm điểm
   */
  private async checkScoringPermission(
    tenantId: number,
    userId: number,
    todo: Todo,
  ): Promise<boolean> {
    // Trường hợp 1: Người tạo công việc (nếu khác với người được giao)
    if (todo.createdBy === userId && todo.assigneeId !== userId) {
      return true;
    }

    // Trường hợp 2: Admin của dự án (nếu công việc thuộc về dự án)
    if (todo.categoryId) {
      const projectMember =
        await this.projectMemberRepository.findByProjectIdAndUserId(
          tenantId,
          todo.categoryId,
          userId,
        );
      if (projectMember && projectMember.role === ProjectMemberRole.ADMIN) {
        return true;
      }
    }

    // TODO: Trường hợp 3: Cấp trên trực tiếp của người được giao nhiệm vụ
    // Cần tích hợp với module phòng ban để kiểm tra quan hệ cấp trên-cấp dưới

    return false;
  }

  /**
   * Lấy lịch sử chấm điểm của công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param todoId ID công việc
   * @returns Danh sách bản ghi chấm điểm
   */
  async getTodoScoreHistory(
    tenantId: number,
    todoId: number,
  ): Promise<TodoScore[]> {
    try {
      // Kiểm tra công việc tồn tại
      await this.findTodoById(tenantId, todoId);

      // Lấy lịch sử chấm điểm
      return await this.todoScoreRepository.findByTodoId(tenantId, todoId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy lịch sử chấm điểm: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_SCORE_HISTORY_FAILED,
        `Lấy lịch sử chấm điểm thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách công việc quá hạn cho chat tool
   * @param tenantId ID tenant (required for tenant isolation)
   * @param options Tùy chọn lọc
   * @returns Danh sách công việc quá hạn
   */
  async getOverdueTasks(
    tenantId: number,
    options: {
      assigneeId?: number;
      departmentId?: number;
      limit?: number;
    } = {},
  ): Promise<Todo[]> {
    try {
      // TODO: Cần thêm trường dueDate vào entity Todo để kiểm tra quá hạn
      // Hiện tại sử dụng logic tạm thời
      const query: TodoQueryDto = {
        page: 1,
        limit: options.limit || 10,
        assigneeId: options.assigneeId,
        status: TodoStatus.PENDING, // Chỉ lấy công việc chưa hoàn thành
      };

      const result = await this.findAllTodos(tenantId, query);

      // TODO: Lọc theo dueDate khi có trường này
      // Hiện tại trả về tất cả pending tasks
      return result.items;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy danh sách công việc quá hạn: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
        `Lấy danh sách công việc quá hạn thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách công việc của người dùng cho chat tool
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId ID người dùng
   * @param options Tùy chọn lọc
   * @returns Danh sách công việc
   */
  async getUserTasks(
    tenantId: number,
    userId: number,
    options: {
      status?: string;
      priority?: string;
      dueDate?: string;
      limit?: number;
    } = {},
  ): Promise<Todo[]> {
    try {
      const query: TodoQueryDto = {
        page: 1,
        limit: options.limit || 10,
        assigneeId: userId,
        status: options.status as TodoStatus,
        priority: options.priority as TodoPriority,
      };

      const result = await this.findAllTodos(tenantId, query);
      return result.items;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy danh sách công việc của người dùng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
        `Lấy danh sách công việc của người dùng thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết công việc cho chat tool
   * @param tenantId ID tenant (required for tenant isolation)
   * @param taskId ID công việc
   * @param options Tùy chọn
   * @returns Chi tiết công việc
   */
  async getTaskDetails(
    tenantId: number,
    taskId: number,
    options: {
      includeSubtasks?: boolean;
      includeComments?: boolean;
      includeAttachments?: boolean;
    } = {},
  ): Promise<Todo> {
    try {
      const todo = await this.findTodoById(tenantId, taskId);

      // TODO: Tích hợp với các module khác để lấy thêm thông tin
      // - Subtasks: sử dụng findSubtasks
      // - Comments: cần tích hợp với module comments
      // - Attachments: cần tích hợp với module attachments

      if (options.includeSubtasks) {
        // Có thể mở rộng để include subtasks
        this.logger.debug(`Including subtasks for todo ${taskId}`);
      }

      return todo;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy chi tiết công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_NOT_FOUND,
        `Lấy chi tiết công việc thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều công việc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId ID người dùng xóa
   * @param dto DTO chứa danh sách ID công việc cần xóa
   * @returns Kết quả xóa nhiều công việc
   */
  async bulkDeleteTodos(
    tenantId: number,
    userId: number,
    dto: BulkDeleteTodoDto,
  ): Promise<BulkDeleteTodoResponseDto> {
    try {
      const { ids } = dto;

      // Tìm tất cả công việc theo danh sách ID
      const todos = await this.todoRepository.findByIds(tenantId, ids);
      const foundIds = todos.map((todo) => todo.id);
      const notFoundIds = ids.filter((id) => !foundIds.includes(id));

      const result: BulkDeleteTodoResponseDto = {
        totalRequested: ids.length,
        successCount: 0,
        failureCount: 0,
        deletedIds: [],
        failures: [],
      };

      // Thêm các ID không tìm thấy vào danh sách lỗi
      notFoundIds.forEach((id) => {
        result.failures.push({
          id,
          reason: 'Không tìm thấy công việc',
        });
        result.failureCount++;
      });

      // Kiểm tra quyền xóa và xóa từng công việc
      for (const todo of todos) {
        try {
          // Kiểm tra quyền xóa (chỉ người tạo mới có quyền xóa)
          if (todo.createdBy !== userId) {
            result.failures.push({
              id: todo.id,
              reason: 'Bạn không có quyền xóa công việc này',
            });
            result.failureCount++;
            continue;
          }

          // Xóa công việc
          const deleted = await this.todoRepository.delete(tenantId, todo.id);
          if (deleted) {
            result.deletedIds.push(todo.id);
            result.successCount++;
          } else {
            result.failures.push({
              id: todo.id,
              reason: 'Xóa công việc thất bại',
            });
            result.failureCount++;
          }
        } catch (error) {
          result.failures.push({
            id: todo.id,
            reason: `Lỗi khi xóa: ${error.message}`,
          });
          result.failureCount++;
        }
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa nhiều công việc: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TODO_DELETE_FAILED,
        `Xóa nhiều công việc thất bại: ${error.message}`,
      );
    }
  }
}
