import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';
import { UserStatus } from '@/modules/auth/enum/user-status.enum';

/**
 * DTO cho truy vấn danh sách người dùng
 */
export class UserQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái người dùng
   * @example "ACTIVE"
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái người dùng',
    enum: UserStatus,
    required: false,
    example: UserStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(UserStatus, { message: 'Trạng thái người dùng không hợp lệ' })
  status?: UserStatus;

  /**
   * Lọc theo ID phòng ban
   * @example 1
   */
  @ApiProperty({
    description: 'Lọc theo ID phòng ban',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID phòng ban phải là số nguyên' })
  @Min(1, { message: 'ID phòng ban phải lớn hơn 0' })
  departmentId?: number;

  /**
   * Lọc theo ID nhân viên (có liên kết với nhân viên hay không)
   * @example 1
   */
  @ApiProperty({
    description: 'Lọc theo ID nhân viên (có liên kết với nhân viên hay không)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'ID nhân viên phải là số nguyên' })
  @Min(1, { message: 'ID nhân viên phải lớn hơn 0' })
  employeeId?: number;

  /**
   * Lọc người dùng có liên kết với nhân viên
   * @example true
   */
  @ApiProperty({
    description: 'Lọc người dùng có liên kết với nhân viên (true) hoặc không có (false)',
    example: true,
    required: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  hasEmployee?: boolean;

  /**
   * Loại người dùng
   * @example "EMPLOYEE"
   */
  @ApiProperty({
    description: 'Lọc theo loại người dùng',
    example: 'EMPLOYEE',
    required: false,
  })
  @IsOptional()
  userType?: string;
}
