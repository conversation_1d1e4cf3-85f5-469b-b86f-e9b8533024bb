import { ApiProperty } from '@nestjs/swagger';
import { UserStatus } from '@/modules/auth/enum/user-status.enum';

/**
 * DTO cho phản hồi thông tin người dùng
 */
export class UserResponseDto {
  /**
   * ID người dùng
   * @example 1
   */
  @ApiProperty({ description: 'ID người dùng', example: 1 })
  id: number;

  /**
   * Địa chỉ email
   * @example "<EMAIL>"
   */
  @ApiProperty({ description: 'Địa chỉ email', example: '<EMAIL>' })
  email: string;

  /**
   * Họ và tên đầy đủ
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Họ và tên đầy đủ',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  fullName: string | null;

  /**
   * ID nhân viên liên kết
   * @example 1
   */
  @ApiProperty({
    description: 'ID nhân viên liên kết',
    example: 1,
    nullable: true,
  })
  employeeId: number | null;

  /**
   * ID phòng ban
   * @example 1
   */
  @ApiProperty({
    description: 'ID phòng ban',
    example: 1,
    nullable: true,
  })
  departmentId: number | null;

  /**
   * Tên phòng ban
   * @example "Phòng Kỹ thuật"
   */
  @ApiProperty({
    description: 'Tên phòng ban',
    example: 'Phòng Kỹ thuật',
    nullable: true,
  })
  departmentName: string | null;

  /**
   * Trạng thái tài khoản
   * @example "ACTIVE"
   */
  @ApiProperty({
    description: 'Trạng thái tài khoản',
    enum: UserStatus,
    example: UserStatus.ACTIVE,
    nullable: true,
  })
  status: UserStatus | null;

  /**
   * Vị trí công việc
   * @example "Lập trình viên"
   */
  @ApiProperty({
    description: 'Vị trí công việc',
    example: 'Lập trình viên',
    nullable: true,
  })
  position: string | null;

  /**
   * Số điện thoại
   * @example "0123456789"
   */
  @ApiProperty({
    description: 'Số điện thoại',
    example: '0123456789',
    nullable: true,
  })
  phoneNumber: string | null;

  /**
   * Địa chỉ
   * @example "123 Đường ABC, Quận 1, TP.HCM"
   */
  @ApiProperty({
    description: 'Địa chỉ',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    nullable: true,
  })
  address: string | null;

  /**
   * Ngày sinh (timestamp)
   * @example 631152000000
   */
  @ApiProperty({
    description: 'Ngày sinh (timestamp)',
    example: 631152000000,
    nullable: true,
  })
  dateOfBirth: number | null;

  /**
   * Giới tính
   * @example "Nam"
   */
  @ApiProperty({
    description: 'Giới tính',
    example: 'Nam',
    nullable: true,
  })
  gender: string | null;

  /**
   * Loại người dùng
   * @example "EMPLOYEE"
   */
  @ApiProperty({
    description: 'Loại người dùng',
    example: 'EMPLOYEE',
    nullable: true,
  })
  userType: string | null;

  /**
   * Thời gian tạo (timestamp)
   * @example 1640995200000
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1640995200000,
    nullable: true,
  })
  createdAt: number | null;

  /**
   * Mã nhân viên (nếu có liên kết)
   * @example "REDAI001"
   */
  @ApiProperty({
    description: 'Mã nhân viên (nếu có liên kết)',
    example: 'REDAI001',
    nullable: true,
  })
  employeeCode: string | null;

  /**
   * Tên nhân viên (nếu có liên kết)
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Tên nhân viên (nếu có liên kết)',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  employeeName: string | null;
}
