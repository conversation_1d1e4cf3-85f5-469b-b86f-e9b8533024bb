import { ApiProperty } from '@nestjs/swagger';
import {
  IsInt,
  IsNotEmpty,
  IsString,
  Min,
  MaxLength,
  IsOptional,
} from 'class-validator';

/**
 * DTO cho xác nhận upload thành công
 */
export class ConfirmUploadDto {
  /**
   * ID của công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của công việc',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'ID công việc không được để trống' })
  @IsInt({ message: 'ID công việc phải là số nguyên' })
  @Min(1, { message: 'ID công việc phải lớn hơn 0' })
  todoId: number;

  /**
   * Key của file trên S3/Cloud Storage
   * @example "todos/attachments/123/1640995200000_document.pdf"
   */
  @ApiProperty({
    description: 'Key của file trên S3/Cloud Storage',
    example: 'todos/attachments/123/1640995200000_document.pdf',
    required: true,
  })
  @IsNotEmpty({ message: 'S3 key không được để trống' })
  @IsString({ message: 'S3 key phải là chuỗi' })
  @MaxLength(500, { message: 'S3 key không được vượt quá 500 ký tự' })
  s3Key: string;

  /**
   * Tên tệp đính kèm
   * @example "tài liệu dự án.pdf"
   */
  @ApiProperty({
    description: 'Tên tệp đính kèm',
    example: 'tài liệu dự án.pdf',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên tệp không được để trống' })
  @IsString({ message: 'Tên tệp phải là chuỗi' })
  @MaxLength(255, { message: 'Tên tệp không được vượt quá 255 ký tự' })
  fileName: string;

  /**
   * Loại nội dung của tệp (MIME type)
   * @example "application/pdf"
   */
  @ApiProperty({
    description: 'Loại nội dung của tệp (MIME type)',
    example: 'application/pdf',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Loại nội dung phải là chuỗi' })
  @MaxLength(100, { message: 'Loại nội dung không được vượt quá 100 ký tự' })
  contentType?: string;

  /**
   * Kích thước tệp thực tế (byte)
   * @example 1024000
   */
  @ApiProperty({
    description: 'Kích thước tệp thực tế (byte)',
    example: 1024000,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Kích thước phải là số nguyên' })
  @Min(0, { message: 'Kích thước không được âm' })
  size?: number;

  /**
   * ID upload để track (optional)
   * @example "upload_1640995200000_abc123"
   */
  @ApiProperty({
    description: 'ID upload để track (optional)',
    example: 'upload_1640995200000_abc123',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Upload ID phải là chuỗi' })
  @MaxLength(100, { message: 'Upload ID không được vượt quá 100 ký tự' })
  uploadId?: string;
}
