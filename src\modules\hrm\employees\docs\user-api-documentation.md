# API Quản lý Người dùng - HRM Module

## Tổng quan
API quản lý người dùng trong module HRM với đầy đủ tính năng phân trang, lọc và tìm kiếm.

## Endpoints

### 1. <PERSON><PERSON><PERSON> danh sách người dùng
```
GET /api/hrm/employees/users
```

#### Query Parameters
| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| page | number | No | Số trang (default: 1) | 1 |
| limit | number | No | Số lượng per page (default: 10) | 20 |
| search | string | No | Tìm kiếm theo tên, email, vị trí | "Nguyễn" |
| sortBy | string | No | Trường sắp xếp (default: createdAt) | "fullName" |
| sortDirection | string | No | Hướng sắp xếp: ASC/DESC (default: DESC) | "ASC" |
| status | string | No | Lọc theo trạng thái | "ACTIVE" |
| departmentId | number | No | Lọc theo phòng ban | 1 |
| employeeId | number | No | Lọc theo ID nhân viên | 1 |
| hasEmployee | boolean | No | Lọc có liên kết nhân viên | true |
| userType | string | No | Lọc theo loại người dùng | "EMPLOYEE" |

#### Response Example
```json
{
  "success": true,
  "message": "Lấy danh sách người dùng thành công",
  "data": {
    "items": [
      {
        "id": 1,
        "email": "<EMAIL>",
        "fullName": "Nguyễn Văn A",
        "employeeId": 1,
        "departmentId": 1,
        "departmentName": "Phòng Kỹ thuật",
        "status": "ACTIVE",
        "position": "Lập trình viên",
        "phoneNumber": "0123456789",
        "address": "123 Đường ABC, Quận 1, TP.HCM",
        "dateOfBirth": 631152000000,
        "gender": "Nam",
        "userType": "EMPLOYEE",
        "createdAt": 1640995200000,
        "employeeCode": "REDAI001",
        "employeeName": "Nguyễn Văn A"
      }
    ],
    "meta": {
      "totalItems": 50,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 5,
      "currentPage": 1
    }
  }
}
```

### 2. Lấy chi tiết người dùng
```
GET /api/hrm/employees/users/{id}
```

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | number | Yes | ID người dùng |

#### Response Example
```json
{
  "success": true,
  "message": "Lấy chi tiết người dùng thành công",
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "fullName": "Nguyễn Văn A",
    "employeeId": 1,
    "departmentId": 1,
    "departmentName": "Phòng Kỹ thuật",
    "status": "ACTIVE",
    "position": "Lập trình viên",
    "phoneNumber": "0123456789",
    "address": "123 Đường ABC, Quận 1, TP.HCM",
    "dateOfBirth": 631152000000,
    "gender": "Nam",
    "userType": "EMPLOYEE",
    "createdAt": 1640995200000,
    "employeeCode": "REDAI001",
    "employeeName": "Nguyễn Văn A"
  }
}
```

## Validation & Business Rules

### Query Parameters Validation
- `page`: Phải >= 1
- `limit`: Phải >= 1
- `departmentId`: Phải là số nguyên > 0
- `employeeId`: Phải là số nguyên > 0
- `status`: Phải thuộc enum UserStatus (ACTIVE, INACTIVE, PENDING, SUSPENDED)

### Security & Permissions
- ✅ **JWT Authentication**: Yêu cầu Bearer token
- ✅ **Tenant Isolation**: Chỉ lấy user thuộc tenant hiện tại
- ✅ **Error Handling**: Xử lý lỗi chi tiết với mã lỗi cụ thể

### Features
- ✅ **Pagination**: Hỗ trợ phân trang với meta information
- ✅ **Search**: Tìm kiếm theo tên, email, vị trí
- ✅ **Filtering**: Lọc theo trạng thái, phòng ban, nhân viên
- ✅ **Sorting**: Sắp xếp theo bất kỳ trường nào
- ✅ **Related Data**: Tự động load tên phòng ban và thông tin nhân viên

## Error Codes
- `USER_NOT_FOUND`: Không tìm thấy người dùng
- `USER_FETCH_FAILED`: Lỗi khi lấy danh sách/chi tiết người dùng

## Usage Examples

### Lấy danh sách user active có phân trang
```bash
curl -X GET "http://localhost:3000/api/hrm/employees/users?page=1&limit=20&status=ACTIVE" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Tìm kiếm user theo tên
```bash
curl -X GET "http://localhost:3000/api/hrm/employees/users?search=Nguyễn&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Lọc user theo phòng ban
```bash
curl -X GET "http://localhost:3000/api/hrm/employees/users?departmentId=1&sortBy=fullName&sortDirection=ASC" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Lấy user có liên kết với nhân viên
```bash
curl -X GET "http://localhost:3000/api/hrm/employees/users?hasEmployee=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Performance Notes
- API sử dụng database indexing cho các trường thường xuyên query
- Lazy loading cho related data (department, employee)
- Caching có thể được implement ở service layer nếu cần
