import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { UserService } from '../services/user.service';
import { UserQueryDto } from '../dto/user-query.dto';
import { UserResponseDto } from '../dto/user-response.dto';

/**
 * Controller cho việc quản lý người dùng
 */
@ApiTags(SWAGGER_API_TAG.EMPLOYEE)
@ApiExtraModels(ApiResponseDto, UserResponseDto)
@Controller('api/hrm/employees/users-management')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserController {
  constructor(private readonly userService: UserService) {}

  /**
   * Lấy danh sách người dùng
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách người dùng với phân trang và lọc' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách người dùng',
    schema: ApiResponseDto.getPaginatedSchema(UserResponseDto),
  })
  async findAllUsers(
    @CurrentUser() user: JwtPayload,
    @Query() query: UserQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<UserResponseDto>>> {
    const paginatedUsers = await this.userService.findAllUsers(
      Number(user.tenantId),
      query,
    );
    return ApiResponseDto.paginated(
      paginatedUsers,
      'Lấy danh sách người dùng thành công',
    );
  }

  /**
   * Lấy chi tiết người dùng
   */
  @Get('/detail/:id')
  @ApiOperation({ summary: 'Lấy chi tiết người dùng' })
  @ApiParam({ name: 'id', description: 'ID người dùng', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết người dùng',
    schema: ApiResponseDto.getSchema(UserResponseDto),
  })
  async findUserById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<UserResponseDto>> {
    const userDetail = await this.userService.findUserById(
      Number(user.tenantId),
      id,
    );
    return ApiResponseDto.success(
      userDetail,
      'Lấy chi tiết người dùng thành công',
    );
  }
}
